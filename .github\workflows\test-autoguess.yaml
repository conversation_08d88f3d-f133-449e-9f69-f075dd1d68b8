name: AutoGuess Tests

on:
  pull_request:
    branches:
      - concedo_experimental
    paths:
      - 'kcpp_adapters/AutoGuess.json'

jobs:
  test-autoguess:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.x'  # Adjust to your preferred Python version

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install requests transformers jinja2 tiktoken protobuf blobfile sentencepiece
        git clone https://github.com/kallewoof/gated-tokenizers.git tests/gated-tokenizers

    - name: Run AutoGuess tests
      run: python tests/test_autoguess.py
